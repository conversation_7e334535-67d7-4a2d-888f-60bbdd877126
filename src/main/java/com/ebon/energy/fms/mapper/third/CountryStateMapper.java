package com.ebon.energy.fms.mapper.third;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.CountryStateDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 国家州Mapper接口
 * 使用MyBatis Plus提供基础CRUD操作
 */
@Mapper
public interface CountryStateMapper extends BaseMapper<CountryStateDO> {

    /**
     * 根据国家代码获取所有州信息
     * @param country 国家名称，如果为null则获取所有国家的州信息
     * @return 州信息列表
     */
    @Select({
            "<script>",
            "SELECT Id, Country, State FROM CountryStates",
            "<if test='country != null and country != \"\"'>",
            "WHERE Country = #{country}",
            "</if>",
            "ORDER BY Country, State",
            "</script>"
    })
    List<CountryStateDO> selectAllStates(@Param("country") String country);
}
