package com.ebon.energy.fms.mapper.third;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.InstallationPhotoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 安装照片Mapper
 * 对应数据库表：InstallationPhotos
 */
@Mapper
public interface InstallationPhotoMapper extends BaseMapper<InstallationPhotoDO> {

    /**
     * 根据安装ID查询照片
     * 
     * @param installationId 安装ID
     * @return 照片列表
     */
    @Select("SELECT Id, InstallationId, Featuring, DateCreated, Uri, ThumbnailUri, UserId " +
            "FROM InstallationPhotos " +
            "WHERE InstallationId = #{installationId} " +
            "ORDER BY DateCreated DESC")
    List<InstallationPhotoDO> selectByInstallationId(@Param("installationId") Integer installationId);

    /**
     * 根据用户ID和安装ID查询照片
     * 
     * @param userId 用户ID
     * @param installationId 安装ID
     * @return 照片列表
     */
    @Select("SELECT Id, InstallationId, Featuring, DateCreated, Uri, ThumbnailUri, UserId " +
            "FROM InstallationPhotos " +
            "WHERE UserId = #{userId} AND InstallationId = #{installationId} " +
            "ORDER BY DateCreated DESC")
    List<InstallationPhotoDO> selectByUserIdAndInstallationId(
            @Param("userId") String userId, 
            @Param("installationId") Integer installationId);

    /**
     * 根据产品序列号查询照片（通过关联查询）
     * 
     * @param serialNumber 产品序列号
     * @return 照片列表
     */
    @Select("SELECT p.Id, p.InstallationId, p.Featuring, p.DateCreated, p.Uri, p.ThumbnailUri, p.UserId " +
            "FROM InstallationPhotos p " +
            "INNER JOIN RedbackProductInstallations i ON p.InstallationId = i.Id " +
            "WHERE i.RedbackProductSn = #{serialNumber} " +
            "AND i.InstallationEndDate IS NULL " +
            "ORDER BY p.DateCreated DESC")
    List<InstallationPhotoDO> selectBySerialNumber(@Param("serialNumber") String serialNumber);

    /**
     * 根据用户ID和产品序列号查询照片
     * 
     * @param userId 用户ID
     * @param serialNumber 产品序列号
     * @return 照片列表
     */
    @Select("SELECT p.Id, p.InstallationId, p.Featuring, p.DateCreated, p.Uri, p.ThumbnailUri, p.UserId " +
            "FROM InstallationPhotos p " +
            "INNER JOIN RedbackProductInstallations i ON p.InstallationId = i.Id " +
            "WHERE p.UserId = #{userId} " +
            "AND i.RedbackProductSn = #{serialNumber} " +
            "AND i.InstallationEndDate IS NULL " +
            "ORDER BY p.DateCreated DESC")
    List<InstallationPhotoDO> selectByUserIdAndSerialNumber(
            @Param("userId") String userId, 
            @Param("serialNumber") String serialNumber);
}
