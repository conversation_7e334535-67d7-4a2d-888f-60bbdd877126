package com.ebon.energy.fms.mapper.third;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.InstallationPhotoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 安装照片Mapper
 * 对应数据库表：InstallationPhotos
 */
@Mapper
public interface InstallationPhotoMapper extends BaseMapper<InstallationPhotoDO> {

    /**
     * 根据安装ID查询照片
     * 对应C#代码：Db.InstallationPhoto.Where(x => x.InstallationId == installationId).OrderBy(x => x.DateAddedUtc)
     *
     * @param installationId 安装ID
     * @return 照片列表
     */
    @Select("SELECT Id, InstallationId, Featuring, DateAddedUtc, Uri, ThumbnailUri, DeletionRequestedOnUtc, UserId " +
            "FROM InstallationPhoto " +
            "WHERE InstallationId = #{installationId} " +
            "ORDER BY DateAddedUtc ASC")
    List<InstallationPhotoDO> selectByInstallationId(@Param("installationId") Integer installationId);

    /**
     * 根据用户ID和安装ID查询照片
     *
     * @param userId 用户ID
     * @param installationId 安装ID
     * @return 照片列表
     */
    @Select("SELECT Id, InstallationId, Featuring, DateAddedUtc, Uri, ThumbnailUri, DeletionRequestedOnUtc, UserId " +
            "FROM InstallationPhoto " +
            "WHERE UserId = #{userId} AND InstallationId = #{installationId} " +
            "ORDER BY DateAddedUtc ASC")
    List<InstallationPhotoDO> selectByUserIdAndInstallationId(
            @Param("userId") String userId,
            @Param("installationId") Integer installationId);

    /**
     * 根据产品序列号查询照片（通过关联查询）
     *
     * @param serialNumber 产品序列号
     * @return 照片列表
     */
    @Select("SELECT p.Id, p.InstallationId, p.Featuring, p.DateAddedUtc, p.Uri, p.ThumbnailUri, p.DeletionRequestedOnUtc, p.UserId " +
            "FROM InstallationPhoto p " +
            "INNER JOIN RedbackProductInstallations i ON p.InstallationId = i.Id " +
            "WHERE i.RedbackProductSn = #{serialNumber} " +
            "AND i.InstallationEndDate IS NULL " +
            "ORDER BY p.DateAddedUtc ASC")
    List<InstallationPhotoDO> selectBySerialNumber(@Param("serialNumber") String serialNumber);

    /**
     * 根据用户ID和产品序列号查询照片
     *
     * @param userId 用户ID
     * @param serialNumber 产品序列号
     * @return 照片列表
     */
    @Select("SELECT p.Id, p.InstallationId, p.Featuring, p.DateAddedUtc, p.Uri, p.ThumbnailUri, p.DeletionRequestedOnUtc, p.UserId " +
            "FROM InstallationPhoto p " +
            "INNER JOIN RedbackProductInstallations i ON p.InstallationId = i.Id " +
            "WHERE p.UserId = #{userId} " +
            "AND i.RedbackProductSn = #{serialNumber} " +
            "AND i.InstallationEndDate IS NULL " +
            "ORDER BY p.DateAddedUtc ASC")
    List<InstallationPhotoDO> selectByUserIdAndSerialNumber(
            @Param("userId") String userId,
            @Param("serialNumber") String serialNumber);
}
