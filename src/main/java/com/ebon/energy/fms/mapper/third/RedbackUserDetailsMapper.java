package com.ebon.energy.fms.mapper.third;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.RedbackUserDetailsDO;
import com.ebon.energy.fms.domain.entity.TelemetryDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface RedbackUserDetailsMapper extends BaseMapper<RedbackUserDetailsDO> {

    /**
     * 根据用户ID和产品序列号查询用户详情
     * 对应C#代码：user.RedbackUserDetails.Where(d => d.RedbackProductSn == serialNumber && d.RedbackUserId == loggedInUserId)
     *
     * @param redbackUserId 用户ID
     * @param redbackProductSn 产品序列号
     * @return 用户详情列表
     */
    @Select("SELECT RedbackUserId, Name, RedbackProductSn, Value " +
            "FROM RedbackUserDetails " +
            "WHERE RedbackUserId = #{redbackUserId} " +
            "AND RedbackProductSn = #{redbackProductSn}")
    List<RedbackUserDetailsDO> selectByUserIdAndProductSn(
            @Param("redbackUserId") String redbackUserId,
            @Param("redbackProductSn") String redbackProductSn);

    /**
     * 根据用户ID查询所有用户详情
     *
     * @param redbackUserId 用户ID
     * @return 用户详情列表
     */
    @Select("SELECT RedbackUserId, Name, RedbackProductSn, Value " +
            "FROM RedbackUserDetails " +
            "WHERE RedbackUserId = #{redbackUserId}")
    List<RedbackUserDetailsDO> selectByUserId(@Param("redbackUserId") String redbackUserId);
}