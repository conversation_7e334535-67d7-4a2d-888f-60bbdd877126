package com.ebon.energy.fms.util;

import com.ebon.energy.fms.domain.entity.RedbackProductInstallationDO;
import com.ebon.energy.fms.domain.entity.RedbackProductInstallationDetailsDO;
import com.ebon.energy.fms.domain.vo.ProductRegistrationDTO;
import com.ebon.energy.fms.domain.vo.detail.SolarPanel;
import com.ebon.energy.fms.mapper.third.RedbackProductInstallationDetailsMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * 安装工具类
 * 用于处理产品安装相关的业务逻辑
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class InstallationUtilities {

    private final RedbackProductInstallationDetailsMapper installationDetailsMapper;

    /**
     * 检查太阳能板阵列配置
     * 对应C#方法：CheckArray
     * 
     * @param installation 安装信息
     * @param installationDto 安装DTO
     * @param arrayIndex 阵列索引 (0, 1, 2)
     */
    public void checkArray(RedbackProductInstallationDO installation, ProductRegistrationDTO installationDto, int arrayIndex) {
        if (installationDto.getSolarPanels() == null || installationDto.getSolarPanels().isEmpty()) {
            return;
        }

        // 确保索引有效
        if (arrayIndex < 0 || arrayIndex >= installationDto.getSolarPanels().size()) {
            return;
        }

        SolarPanel solarPanel = installationDto.getSolarPanels().get(arrayIndex);
        if (solarPanel == null) {
            return;
        }

        // 更新或插入PVSize详情
        String pvSizeName = "PVSize" + (arrayIndex + 1);
        String pvSizeValue = Optional.ofNullable(solarPanel.getPvSize())
                .map(Object::toString)
                .orElse("");

        updateOrInsertInstallationDetail(installation.getId(), pvSizeName, pvSizeValue);

        // 更新或插入PVCount详情
        String pvCountName = "PVCount" + (arrayIndex + 1);
        String pvCountValue = Optional.ofNullable(solarPanel.getPvCount())
                .map(Object::toString)
                .orElse("");

        updateOrInsertInstallationDetail(installation.getId(), pvCountName, pvCountValue);

        log.info("Updated solar panel array {} for installation {}", arrayIndex + 1, installation.getId());
    }

    /**
     * 检查制造商名称
     * 对应C#方法：CheckManufacturerName
     * 
     * @param installation 安装信息
     * @param installationDto 安装DTO
     */
    public void checkManufacturerName(RedbackProductInstallationDO installation, ProductRegistrationDTO installationDto) {
        if (!StringUtils.hasText(installationDto.getSolarPanelManufacturer())) {
            return;
        }

        updateOrInsertInstallationDetail(installation.getId(), "SolarPanelManufacturer", installationDto.getSolarPanelManufacturer());
        log.info("Updated solar panel manufacturer for installation {}", installation.getId());
    }

    /**
     * 检查太阳能板类型
     * 对应C#方法：CheckSolarPanelType
     * 
     * @param installation 安装信息
     * @param installationDto 安装DTO
     */
    public void checkSolarPanelType(RedbackProductInstallationDO installation, ProductRegistrationDTO installationDto) {
        if (!StringUtils.hasText(installationDto.getSolarPanelType())) {
            return;
        }

        updateOrInsertInstallationDetail(installation.getId(), "SolarPanelType", installationDto.getSolarPanelType());
        log.info("Updated solar panel type for installation {}", installation.getId());
    }

    /**
     * 更新或插入安装详情
     * 
     * @param installationId 安装ID
     * @param name 详情名称
     * @param value 详情值
     */
    private void updateOrInsertInstallationDetail(Integer installationId, String name, String value) {
        // 查询是否已存在该详情
        RedbackProductInstallationDetailsDO existingDetail = installationDetailsMapper
                .selectByInstallationIdAndName(installationId, name);

        if (existingDetail != null) {
            // 更新现有记录
            existingDetail.setValue(value);
            installationDetailsMapper.updateById(existingDetail);
        } else {
            // 插入新记录
            RedbackProductInstallationDetailsDO newDetail = new RedbackProductInstallationDetailsDO();
            newDetail.setRedbackProductInstallationId(installationId);
            newDetail.setName(name);
            newDetail.setValue(value);
            installationDetailsMapper.insert(newDetail);
        }
    }
}
