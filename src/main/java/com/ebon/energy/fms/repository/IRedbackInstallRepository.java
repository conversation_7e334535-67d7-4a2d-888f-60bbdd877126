package com.ebon.energy.fms.repository;

import com.ebon.energy.fms.common.enums.UserPreferenceKey;
import com.ebon.energy.fms.domain.vo.ProductRegistrationDTO;
import com.ebon.energy.fms.domain.vo.detail.InstallationPhotoDescriptorDto;
import com.ebon.energy.fms.domain.vo.detail.RedbackInstallation;

import java.util.List;
import java.util.Map;

/**
 * Redback仓储接口
 * 定义Redback产品相关的数据访问方法
 */
public interface IRedbackInstallRepository {

    /**
     * 获取Redback安装信息
     * @param userId 用户ID
     * @param serialNumber 序列号
     * @return Redback安装信息
     */
    RedbackInstallation getRedbackInstallation(String userId, String serialNumber);

    /**
     * 更新Redback安装信息
     * @param userId 用户ID
     * @param installation 安装信息
     */
    void updateRedbackInstallation(String userId, ProductRegistrationDTO installation);

    /**
     * 检查是否可以更新安装信息
     * @param userId 用户ID
     * @param installation 安装信息
     * @return 如果可以更新返回true，否则返回false
     */
    boolean canUpdateInstallation(String userId, ProductRegistrationDTO installation);

    /**
     * 获取照片列表
     * @param userId 用户ID
     * @param serialNumber 序列号
     * @return 照片列表
     */
    List<InstallationPhotoDescriptorDto> getPhotosForAsync(String userId, String serialNumber);

    /**
     * 获取用户偏好设置
     * @param userId 用户ID
     * @param serialNumber 序列号
     * @return 用户偏好设置映射
     */
    Map<UserPreferenceKey, String> getUserPreferences(String userId, String serialNumber);

    /**
     * 更新用户偏好设置
     * @param userId 用户ID
     * @param serialNumber 序列号
     * @param preferences 偏好设置映射
     */
    void updateUserPreferences(String userId, String serialNumber, Map<UserPreferenceKey, String> preferences);
}
