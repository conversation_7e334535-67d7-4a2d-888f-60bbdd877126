package com.ebon.energy.fms.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.ebon.energy.fms.common.enums.HardwareFamilyEnum;
import com.ebon.energy.fms.common.enums.HardwareModelEnum;
import com.ebon.energy.fms.common.enums.UniversalSettingSource;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.common.utils.CommonFunctions;
import com.ebon.energy.fms.common.utils.ModelInfo;
import com.ebon.energy.fms.common.utils.SettingsReaderProvider;
import com.ebon.energy.fms.common.utils.Version;
import com.ebon.energy.fms.domain.vo.DeviceSettingsIntentVO;
import com.ebon.energy.fms.domain.vo.product.control.*;
import com.ebon.energy.fms.domain.vo.product.control.invert.DesiredAndReported;
import com.ebon.energy.fms.domain.vo.product.control.invert.DeviceInfoAndSettings;
import com.ebon.energy.fms.mapper.third.DeviceInfoAndSettingsMapper;
import com.ebon.energy.fms.mapper.third.SpecificationMapper;
import com.ebon.energy.fms.repository.ISpecificationRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

@Slf4j
@Repository
@RequiredArgsConstructor
public class SpecificationRepository implements ISpecificationRepository {
    private final ObjectMapper objectMapper;

    private final DeviceInfoAndSettingsMapper deviceInfoAndSettingsMapper;

    private final SpecificationMapper specificationMapper;

    public InstallationSpecification getInstallationSpecAsync(String serialNumber) {
        var deviceSettings = getDeviceInfoAndSettings(null, serialNumber, null);
        var hardAndFirm = SpecificationFactory.get(deviceSettings.getIdentityCard());
        boolean mustHaveGrid = hardAndFirm != null && Boolean.TRUE.equals(hardAndFirm.isGridTieInverter());

        boolean measuringThirdPartyInverter = false;
        ICommonSettingsReader reader = SettingsReaderProvider.tryGet(deviceSettings);
        if (reader != null) {
            ACCoupledSettingsDto acCoupledSettings = reader.getACCoupledSettings(UniversalSettingSource.DESIRED);
            if (acCoupledSettings != null && acCoupledSettings.getIsACCoupledEnabled() != null) {
                measuringThirdPartyInverter = acCoupledSettings.getIsACCoupledEnabled();
            }
        }

        boolean hasIncorrectLoadandPvDailyCacheValues =
                getHasIncorrectLoadAndPvDailyCacheValues(hardAndFirm.getHardwareFamily(), measuringThirdPartyInverter);

        return new InstallationSpecification(
                hardAndFirm,
                hasIncorrectLoadandPvDailyCacheValues,
                measuringThirdPartyInverter,
                mustHaveGrid
        );
    }

    @SneakyThrows
    public DeviceInfoAndSettings getDeviceInfoAndSettings(
            String userId,
            String serialNumber,
            LocalDateTime asOf) {

        InternalDeviceInfoAndSettings data = deviceInfoAndSettingsMapper.getDeviceInfoAndSettings(userId, serialNumber, asOf);

        if (data == null) {
            data = new InternalDeviceInfoAndSettings();
//            throw new BizException("Data not found for '" + serialNumber + "'");
        }

        if (data.getFirmwareVersion() == null || data.getModelName() == null) {
            // 注册页面会进入这里，所以不能抛出异常
        }
        log.info("Getting device info and settings for serial number: {} - {}", serialNumber,data.getReported());
        RossDesiredSettings settings = StringUtils.isNotBlank(data.getDesired()) ? objectMapper.readValue(data.getDesired(), RossDesiredSettings.class) : null;
        RossReportedSettings reported = StringUtils.isNotBlank(data.getReported()) ? objectMapper.readValue(data.getReported(), RossReportedSettings.class) : null;
        DesiredAndReported desiredAndReported = new DesiredAndReported(settings, reported);

        DeviceSettingsIntentVO intent = null;
        if (data.getIntent() != null && !data.getIntent().isEmpty()) {
            intent = JSONObject.parseObject(data.getIntent(), DeviceSettingsIntentVO.class);
        }

        InverterIdentityCard identityCard = new InverterIdentityCard(
                serialNumber,
                data.getModelName(),
                data.getFirmwareVersion(),
                //todo check parse
                data.getSoftwareVersion() != null ? new RossVersion(Version.parse(data.getSoftwareVersion())) : null,
                data.getHardwareConfig()
        );

        return new DeviceInfoAndSettings(
                desiredAndReported.getReported(),
                desiredAndReported.getDesired(),
                intent,
                identityCard,
                data.asExplicitSettings()
        );
    }

    public static boolean getHasIncorrectLoadAndPvDailyCacheValues(
            HardwareFamilyEnum family,
            boolean isInAcCoupledMode) {
        return family == HardwareFamilyEnum.Inverter_Goodwe_ES && isInAcCoupledMode;
    }

    /**
     * 获取模型信息
     * 基于C#代码逻辑实现的Java版本
     *
     * @param userId 用户ID
     * @param serialNumber 序列号
     * @return 模型信息结果
     * @throws SecurityException 当用户ID为空时抛出
     * @throws BizException 当未找到模型信息时抛出
     */
    @SneakyThrows
    public ModelInfoResult getModelInfoAsync(String userId, String serialNumber) {
        if (CommonFunctions.isNullOrEmpty(userId)) {
            throw new SecurityException("Unauthorized access - user ID is required");
        }

        InternalModelInfo data = specificationMapper.getModelInfo(userId, serialNumber);

        if (data == null) {
            throw new BizException("Model info not found for '" + serialNumber + "'");
        }

        if (CommonFunctions.isNullOrEmpty(data.getFirmwareVersion()) ||
            CommonFunctions.isNullOrEmpty(data.getModelName())) {
            // 注册页面会进入这里，所以不能抛出异常
            log.warn("Incomplete model info for serial number: {} - ModelName: {}, FirmwareVersion: {}",
                    serialNumber, data.getModelName(), data.getFirmwareVersion());
        }

        // 解析设置信息
        RossDesiredSettings desired = null;
        RossReportedSettings reported = null;

        if (StringUtils.isNotBlank(data.getDesired())) {
            desired = objectMapper.readValue(data.getDesired(), RossDesiredSettings.class);
        }

        if (StringUtils.isNotBlank(data.getReported())) {
            reported = objectMapper.readValue(data.getReported(), RossReportedSettings.class);
        }

        DesiredAndReported desiredAndReported = new DesiredAndReported(desired, reported);

        // 获取硬件模型
        HardwareModelEnum hardwareModel = CommonFunctions.getHardwareModel(
                serialNumber,
                desiredAndReported,
                data.getModelName()
        );

        // 创建ModelInfo
        ModelInfo modelInfo = new ModelInfo(
                hardwareModel,
                data.getFirmwareVersion()
        );

        return new ModelInfoResult(modelInfo, desiredAndReported);
    }

    @Override
    public HardwareFirmwareSpecification getSpecAsync(String userId, String serialNumber) {
        // 这个方法使用getModelInfoAsync来获取模型信息，然后通过SpecificationFactory获取规格
        try {
            ModelInfoResult modelInfoResult = getModelInfoAsync(userId, serialNumber);
            return SpecificationFactory.get(modelInfoResult.getModel());
        } catch (Exception e) {
            log.error("Failed to get specification for user: {}, serialNumber: {}", userId, serialNumber, e);
            return null;
        }
    }

    @Override
    public InstallationSpecification getInstallationSpecAsync(String userId, String serialNumber) {
        return getInstallationSpecAsync(serialNumber);
    }
}

