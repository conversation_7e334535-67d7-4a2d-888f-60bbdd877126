package com.ebon.energy.fms.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ebon.energy.fms.common.enums.ConfigurationType;
import com.ebon.energy.fms.common.enums.UserPreferenceKey;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.entity.AddressesDO;
import com.ebon.energy.fms.domain.entity.RedbackProductInstallationDO;
import com.ebon.energy.fms.domain.entity.RedbackProductsDO;
import com.ebon.energy.fms.domain.entity.RedbackUserDO;
import com.ebon.energy.fms.domain.vo.ProductRegistrationDTO;
import com.ebon.energy.fms.domain.vo.detail.InstallationPhotoDescriptorDto;
import com.ebon.energy.fms.domain.vo.detail.RedbackInstallation;
import com.ebon.energy.fms.domain.vo.detail.RedbackProductInstallationDetail;
import com.ebon.energy.fms.mapper.third.AddressesMapper;
import com.ebon.energy.fms.mapper.third.ProductInstallationMapper;
import com.ebon.energy.fms.mapper.third.ProductMapper;
import com.ebon.energy.fms.mapper.third.RedbackUserMapper;
import com.ebon.energy.fms.repository.IRedbackInstallRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Repository
@RequiredArgsConstructor
public class RedbackInstallRepositoryImpl implements IRedbackInstallRepository {

    private final RedbackUserMapper redbackUserMapper;
    private final ProductMapper productMapper;
    private final ProductInstallationMapper productInstallationMapper;
    private final AddressesMapper addressesMapper;

    /**
     * 获取Redback产品安装信息
     * 转换自C#方法：GetRedbackInstallation
     *
     * @param loggedInUserId 登录用户ID
     * @param serialNumber 产品序列号
     * @param activeOnly 是否只查询活跃安装（默认true）
     * @return RedbackInstallation 安装信息
     */
    public RedbackInstallation getRedbackInstallation(String loggedInUserId, String serialNumber, boolean activeOnly) {
        try {
            // #region Data Access - 数据访问部分
            // 查询产品信息
            QueryWrapper<RedbackProductsDO> productQuery = new QueryWrapper<>();
            productQuery.eq("RedbackProductSn", serialNumber);
            RedbackProductsDO product = Optional.ofNullable(productMapper.selectOne(productQuery))
                    .orElseThrow(() -> new BizException("The serial number doesn't exist."));

            // #endregion

            // 查询安装信息
            RedbackProductInstallationDO installation;

            if (activeOnly) {
                // 使用现有的selectBySn方法查询活跃安装
                installation = productInstallationMapper.selectBySn(serialNumber);
            } else {
                // 查询所有安装记录，按开始日期降序排列
                QueryWrapper<RedbackProductInstallationDO> installationQuery = new QueryWrapper<>();
                installationQuery.eq("RedbackProductSn", serialNumber)
                        .orderByDesc("InstallationStartDate");
                List<RedbackProductInstallationDO> installations = productInstallationMapper.selectList(installationQuery);
                installation = installations.isEmpty() ? null : installations.get(0);
            }

            if (installation == null) {
                throw new BizException(String.format("There is no active installation for this product (%s)", serialNumber));
            }

            // 构建返回对象
            return buildRedbackInstallation(installation, product);

        } catch (Exception ex) {
            log.error("Error getting redback installation for user {} and serial {}", loggedInUserId, serialNumber, ex);
            throw new BizException("Failed to get redback installation: " + ex.getMessage());
        }
    }

    @Override
    public RedbackInstallation getRedbackInstallation(String userId, String serialNumber) {
        return getRedbackInstallation(userId, serialNumber, true);
    }

    @Override
    public void updateRedbackInstallation(String userId, ProductRegistrationDTO installation) {

    }

    @Override
    public boolean canUpdateInstallation(String userId, ProductRegistrationDTO installation) {
        return false;
    }

    @Override
    public List<InstallationPhotoDescriptorDto> getPhotosForAsync(String userId, String serialNumber) {
        return List.of();
    }

    @Override
    public Map<UserPreferenceKey, String> getUserPreferences(String userId, String serialNumber) {
        return Map.of();
    }

    @Override
    public void updateUserPreferences(String userId, String serialNumber, Map<UserPreferenceKey, String> preferences) {

    }


    /**
     * 构建RedbackInstallation对象
     */
    private RedbackInstallation buildRedbackInstallation(RedbackProductInstallationDO installation, RedbackProductsDO product) {
        RedbackInstallation result = new RedbackInstallation();
        result.setRedbackProductSn(installation.getRedbackProductSn());
        result.setSiteId(installation.getSiteId());
        result.setInstallationStartDate(Optional.ofNullable(installation.getInstallationStartDate())
                .map(timestamp -> timestamp.toLocalDateTime())
                .orElse(null));

        // 查询地址信息
        if (installation.getAddressId() != null) {
            AddressesDO address = addressesMapper.selectById(installation.getAddressId());
            result.setAddress(address);
        }

        // 设置产品信息
        result.setRedbackProduct(product);

        // 这里需要查询RedbackProductInstallationDetails
        // 由于没有对应的Mapper，暂时设置为空列表
        result.setRedbackProductInstallationDetails(List.of());

        return result;
    }
}
