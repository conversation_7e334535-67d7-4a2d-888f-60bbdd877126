package com.ebon.energy.fms.repository.impl;

import com.ebon.energy.fms.common.enums.UserPreferenceKey;
import com.ebon.energy.fms.domain.vo.ProductRegistrationDTO;
import com.ebon.energy.fms.domain.vo.detail.InstallationPhotoDescriptorDto;
import com.ebon.energy.fms.domain.vo.detail.RedbackInstallation;
import com.ebon.energy.fms.repository.IRedbackInstallRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Slf4j
@Repository
@RequiredArgsConstructor
public class RedbackInstallRepositoryImpl implements IRedbackInstallRepository {

    @Override
    public RedbackInstallation getRedbackInstallation(String userId, String serialNumber) {
        return null;
    }

    @Override
    public void updateRedbackInstallation(String userId, ProductRegistrationDTO installation) {

    }

    @Override
    public boolean canUpdateInstallation(String userId, ProductRegistrationDTO installation) {
        return false;
    }

    @Override
    public List<InstallationPhotoDescriptorDto> getPhotosForAsync(String userId, String serialNumber) {
        return List.of();
    }

    @Override
    public Map<UserPreferenceKey, String> getUserPreferences(String userId, String serialNumber) {
        return Map.of();
    }

    @Override
    public void updateUserPreferences(String userId, String serialNumber, Map<UserPreferenceKey, String> preferences) {

    }
}
