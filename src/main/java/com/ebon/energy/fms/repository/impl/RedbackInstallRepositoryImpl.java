package com.ebon.energy.fms.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ebon.energy.fms.common.enums.ConfigurationType;
import com.ebon.energy.fms.common.enums.PhotoFeaturingEnum;
import com.ebon.energy.fms.common.enums.UserPreferenceKey;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.entity.*;
import com.ebon.energy.fms.domain.vo.ProductRegistrationDTO;
import com.ebon.energy.fms.domain.vo.ProductRegistrationDTOBuddy;
import com.ebon.energy.fms.domain.vo.detail.InstallationPhotoDescriptorDto;
import com.ebon.energy.fms.domain.vo.detail.RedbackInstallation;
import com.ebon.energy.fms.domain.vo.detail.RedbackProductInstallationDetail;
import com.ebon.energy.fms.mapper.third.AddressesMapper;
import com.ebon.energy.fms.mapper.third.InstallationPhotoMapper;
import com.ebon.energy.fms.mapper.third.ProductInstallationMapper;
import com.ebon.energy.fms.mapper.third.ProductMapper;
import com.ebon.energy.fms.mapper.third.RedbackProductInstallationDetailsMapper;
import com.ebon.energy.fms.mapper.third.RedbackUserDetailsMapper;
import com.ebon.energy.fms.mapper.third.RedbackUserMapper;
import com.ebon.energy.fms.repository.IRedbackInstallRepository;
import com.ebon.energy.fms.util.InstallationUtilities;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Repository
@RequiredArgsConstructor
public class RedbackInstallRepositoryImpl implements IRedbackInstallRepository {

    private final RedbackUserMapper redbackUserMapper;
    private final RedbackUserDetailsMapper redbackUserDetailsMapper;
    private final ProductMapper productMapper;
    private final ProductInstallationMapper productInstallationMapper;
    private final AddressesMapper addressesMapper;
    private final RedbackProductInstallationDetailsMapper installationDetailsMapper;
    private final InstallationPhotoMapper installationPhotoMapper;
    private final InstallationUtilities installationUtilities;

    /**
     * 获取Redback产品安装信息
     * 转换自C#方法：GetRedbackInstallation
     *
     * @param loggedInUserId 登录用户ID
     * @param serialNumber 产品序列号
     * @param activeOnly 是否只查询活跃安装（默认true）
     * @return RedbackInstallation 安装信息
     */
    public RedbackInstallation getRedbackInstallation(String loggedInUserId, String serialNumber, boolean activeOnly) {
        try {
            // #region Data Access - 数据访问部分
            // 查询产品信息
            QueryWrapper<RedbackProductsDO> productQuery = new QueryWrapper<>();
            productQuery.eq("RedbackProductSn", serialNumber);
            RedbackProductsDO product = Optional.ofNullable(productMapper.selectOne(productQuery))
                    .orElseThrow(() -> new BizException("The serial number doesn't exist."));

            // 查询安装信息
            RedbackProductInstallationDO installation;

            if (activeOnly) {
                // 使用现有的selectBySn方法查询活跃安装
                installation = productInstallationMapper.selectBySn(serialNumber);
            } else {
                // 查询所有安装记录，按开始日期降序排列
                QueryWrapper<RedbackProductInstallationDO> installationQuery = new QueryWrapper<>();
                installationQuery.eq("RedbackProductSn", serialNumber)
                        .orderByDesc("InstallationStartDate");
                List<RedbackProductInstallationDO> installations = productInstallationMapper.selectList(installationQuery);
                installation = installations.isEmpty() ? null : installations.get(0);
            }

            if (installation == null) {
                throw new BizException(String.format("There is no active installation for this product (%s)", serialNumber));
            }

            // 构建返回对象
            return buildRedbackInstallation(installation, product);

        } catch (Exception ex) {
            log.error("Error getting redback installation for user {} and serial {}", loggedInUserId, serialNumber, ex);
            throw new BizException("Failed to get redback installation: " + ex.getMessage());
        }
    }

    @Override
    public RedbackInstallation getRedbackInstallation(String userId, String serialNumber) {
        return getRedbackInstallation(userId, serialNumber, true);
    }

    /**
     * 更新Redback安装太阳能信息
     * 转换自C#方法：UpdateRedbackInstallationSolarInformation
     *
     * @param loggedInUserId 登录用户ID
     * @param installationDto 安装DTO
     * @param buddy 伴随对象，标识哪些字段被提供
     * @return RedbackProductInstallationDO 更新后的安装信息
     */
    public RedbackProductInstallationDO updateRedbackInstallationSolarInformation(
            String loggedInUserId,
            ProductRegistrationDTO installationDto,
            ProductRegistrationDTOBuddy buddy) {
        try {
            // 验证序列号
            if (!StringUtils.hasText(installationDto.getSerialNumber())) {
                throw new BizException("No serial number supplied.");
            }

            // 查询产品信息
            QueryWrapper<RedbackProductsDO> productQuery = new QueryWrapper<>();
            productQuery.eq("RedbackProductSn", installationDto.getSerialNumber());
            RedbackProductsDO product = Optional.ofNullable(productMapper.selectOne(productQuery))
                    .orElseThrow(() -> new BizException("The serial number doesn't exist."));

            // 查询活跃安装
            RedbackProductInstallationDO installation = productInstallationMapper.selectBySn(installationDto.getSerialNumber());
            if (installation == null) {
                throw new BizException("Could not find an existing installation for this serial number.");
            }

            // 处理太阳能板信息
            if (buddy.isSolarPanelsProvided()) {
                for (int i = 0; i < 3; i++) {
                    installationUtilities.checkArray(installation, installationDto, i);
                }
            }

            if (buddy.isSolarPanelManufacturerProvided()) {
                installationUtilities.checkManufacturerName(installation, installationDto);
            }

            if (buddy.isSolarPanelTypeProvided()) {
                installationUtilities.checkSolarPanelType(installation, installationDto);
            }

            // 更新太阳能配置状态
            updateSolarConfigurationState(installation);

            // 更新安装记录
            productInstallationMapper.updateById(installation);

            log.info("Successfully updated solar information for installation {}", installation.getId());
            return installation;

        } catch (Exception ex) {
            log.error("Error updating redback installation solar information for user {} and serial {}",
                    loggedInUserId, installationDto.getSerialNumber(), ex);
            throw new BizException("Failed to update redback installation: " + ex.getMessage());
        }
    }

    @Override
    public void updateRedbackInstallation(String userId, ProductRegistrationDTO installation) {
        // 创建默认的buddy对象，所有字段都标记为已提供
        ProductRegistrationDTOBuddy buddy = new ProductRegistrationDTOBuddy();
        buddy.setSolarPanelsProvided(true);
        buddy.setSolarPanelManufacturerProvided(true);
        buddy.setSolarPanelTypeProvided(true);

        updateRedbackInstallationSolarInformation(userId, installation, buddy);
    }

    @Override
    public boolean canUpdateInstallation(String userId, ProductRegistrationDTO installation) {
        return true;
    }

    /**
     * 获取安装照片
     * 转换自C#方法：GetPhotosForAsync
     *
     * @param loggedInUserId 登录用户ID
     * @param serialNumber 产品序列号
     * @return List<InstallationPhotoDescriptorDto> 照片描述符列表
     */
    public List<InstallationPhotoDescriptorDto> getPhotosFor(String loggedInUserId, String serialNumber) {
        try {
            // 获取安装信息
            RedbackInstallation installation = getRedbackInstallation(loggedInUserId, serialNumber);

            // 根据安装ID获取照片
            return getPhotosForInstallation(loggedInUserId, installation.getId());

        } catch (Exception ex) {
            log.error("Error getting photos for user {} and serial {}", loggedInUserId, serialNumber, ex);
            throw new BizException("Failed to get photos: " + ex.getMessage());
        }
    }

    @Override
    public List<InstallationPhotoDescriptorDto> getPhotosForAsync(String userId, String serialNumber) {
        // 转换异步方法为同步调用
        return getPhotosFor(userId, serialNumber);
    }

    /**
     * 根据安装ID获取照片
     * 对应C#中的重载方法：GetPhotosForAsync(string loggedInUserId, int installationId)
     * 转换自C#代码：
     * var results = await Db.InstallationPhoto
     *     .Where(x => x.InstallationId == installationId)
     *     .OrderBy(x => x.DateAddedUtc)
     *     .ToListAsync();
     * return results.Where(x => !IsSoftDeleted(x)).Select(Map).ToList();
     *
     * @param loggedInUserId 登录用户ID
     * @param installationId 安装ID
     * @return List<InstallationPhotoDescriptorDto> 照片描述符列表
     */
    public List<InstallationPhotoDescriptorDto> getPhotosForInstallation(String loggedInUserId, Integer installationId) {
        try {
            // 查询照片数据（按DateAddedUtc排序）
            List<InstallationPhotoDO> photoDOList = installationPhotoMapper.selectByInstallationId(installationId);

            // 过滤软删除的照片并转换为DTO
            return photoDOList.stream()
                    .filter(photo -> !isSoftDeleted(photo))  // 过滤软删除的照片
                    .map(this::mapToPhotoDescriptorDto)      // 映射为DTO
                    .collect(Collectors.toList());

        } catch (Exception ex) {
            log.error("Error getting photos for user {} and installation {}", loggedInUserId, installationId, ex);
            throw new BizException("Failed to get photos for installation: " + ex.getMessage());
        }
    }

    /**
     * 获取用户偏好设置
     * 转换自C#方法：GetUserPreferences
     * 对应C#代码：
     * var details = user.RedbackUserDetails
     *     .Where(d => d.RedbackProductSn == serialNumber && d.RedbackUserId == loggedInUserId)
     *     .ToDictionary(d => (UserPreferenceKey)Enum.Parse(typeof(UserPreferenceKey), d.Name), d => d.Value);
     *
     * @param loggedInUserId 登录用户ID
     * @param serialNumber 产品序列号
     * @return Map<UserPreferenceKey, String> 用户偏好设置映射
     */
    @Override
    public Map<UserPreferenceKey, String> getUserPreferences(String loggedInUserId, String serialNumber) {
        try {
            // #region Data access - 数据访问部分

            // 查询登录用户
            RedbackUserDO user = Optional.ofNullable(redbackUserMapper.selectById(loggedInUserId))
                    .orElseThrow(() -> new BizException("Logged-in user doesn't exist."));

            // 查询产品信息
            QueryWrapper<RedbackProductsDO> productQuery = new QueryWrapper<>();
            productQuery.eq("RedbackProductSn", serialNumber);
            RedbackProductsDO product = Optional.ofNullable(productMapper.selectOne(productQuery))
                    .orElseThrow(() -> new BizException("Invalid product serial number."));

            // 查询用户详情
            List<RedbackUserDetailsDO> userDetails = redbackUserDetailsMapper
                    .selectByUserIdAndProductSn(loggedInUserId, serialNumber);

            // 转换为Map<UserPreferenceKey, String>
            Map<UserPreferenceKey, String> preferences = userDetails.stream()
                    .collect(Collectors.toMap(
                            detail -> parseUserPreferenceKey(detail.getName()),
                            RedbackUserDetailsDO::getValue,
                            (existing, replacement) -> replacement  // 如果有重复key，使用新值
                    ));

            return preferences;

        } catch (Exception ex) {
            log.error("Error getting user preferences for user {} and serial {}", loggedInUserId, serialNumber, ex);
            throw new BizException("Failed to get user preferences: " + ex.getMessage());
        }
    }



    @Override
    public void updateUserPreferences(String userId, String serialNumber, Map<UserPreferenceKey, String> preferences) {

    }


    /**
     * 构建RedbackInstallation对象
     */
    private RedbackInstallation buildRedbackInstallation(RedbackProductInstallationDO installation, RedbackProductsDO product) {
        RedbackInstallation result = new RedbackInstallation();
        result.setId(installation.getId());
        result.setRedbackProductSn(installation.getRedbackProductSn());
        result.setSiteId(installation.getSiteId());
        result.setInstallationStartDate(Optional.ofNullable(installation.getInstallationStartDate())
                .map(timestamp -> timestamp.toLocalDateTime())
                .orElse(null));

        // 查询地址信息
        if (installation.getAddressId() != null) {
            AddressesDO address = addressesMapper.selectById(installation.getAddressId());
            result.setAddress(address);
        }

        // 设置产品信息
        result.setRedbackProduct(product);

        // 查询安装详情
        List<RedbackProductInstallationDetailsDO> detailsDOList = installationDetailsMapper.selectByInstallationId(installation.getId());
        List<RedbackProductInstallationDetail> details = detailsDOList.stream()
                .map(this::convertToDetailVO)
                .collect(Collectors.toList());
        result.setRedbackProductInstallationDetails(details);

        return result;
    }

    /**
     * 更新太阳能配置状态
     * 标记安装为已配置状态
     *
     * @param installation 安装信息
     */
    private void updateSolarConfigurationState(RedbackProductInstallationDO installation) {
        // 添加或更新"Configured"状态
        RedbackProductInstallationDetailsDO configuredDetail = installationDetailsMapper
                .selectByInstallationIdAndName(installation.getId(), "SolarConfigured");

        if (configuredDetail != null) {
            configuredDetail.setValue("true");
            installationDetailsMapper.updateById(configuredDetail);
        } else {
            RedbackProductInstallationDetailsDO newDetail = new RedbackProductInstallationDetailsDO();
            newDetail.setRedbackProductInstallationId(installation.getId());
            newDetail.setName("SolarConfigured");
            newDetail.setValue("true");
            installationDetailsMapper.insert(newDetail);
        }

        log.info("Updated solar configuration state for installation {}", installation.getId());
    }

    /**
     * 将DO对象转换为VO对象
     */
    private RedbackProductInstallationDetail convertToDetailVO(RedbackProductInstallationDetailsDO detailsDO) {
        RedbackProductInstallationDetail detail = new RedbackProductInstallationDetail();
        detail.setRedbackProductInstallationId(detailsDO.getRedbackProductInstallationId());
        detail.setName(detailsDO.getName());
        detail.setValue(detailsDO.getValue());
        return detail;
    }

    /**
     * 检查照片是否被软删除
     * 对应C#代码：bool IsSoftDeleted(InstallationPhoto x) => x.DeletionRequestedOnUtc.HasValue;
     *
     * @param photo 照片DO对象
     * @return true如果被软删除，false否则
     */
    private boolean isSoftDeleted(InstallationPhotoDO photo) {
        return photo.getDeletionRequestedOnUtc() != null;
    }

    /**
     * 将照片DO对象映射为DTO对象
     * 对应C#代码：
     * InstallationPhotoDescriptorDto Map(InstallationPhoto photo) {
     *     return new InstallationPhotoDescriptorDto(
     *         id: photo.Id,
     *         uri: new Uri(photo.Uri),
     *         thumbnailUri: photo.ThumbnailUri != null ? new Uri(photo.ThumbnailUri) : null,
     *         featuring: photo.Featuring,
     *         Instant.FromDateTimeUtc(DateTime.SpecifyKind(photo.DateAddedUtc, DateTimeKind.Utc)));
     * }
     *
     * @param photo 照片DO对象
     * @return InstallationPhotoDescriptorDto
     */
    private InstallationPhotoDescriptorDto mapToPhotoDescriptorDto(InstallationPhotoDO photo) {
        // 转换featuring枚举
        PhotoFeaturingEnum featuring = PhotoFeaturingEnum.values()[photo.getFeaturing()];

        // 转换日期为Instant（UTC）
        Instant dateAddedUtc = Optional.ofNullable(photo.getDateAddedUtc())
                .map(timestamp -> timestamp.toInstant())
                .orElse(null);

        return new InstallationPhotoDescriptorDto(
                photo.getId(),
                featuring,
                dateAddedUtc,
                photo.getUri(),
                photo.getThumbnailUri()
        );
    }

    /**
     * 解析用户偏好设置键
     * 对应C#代码：(UserPreferenceKey)Enum.Parse(typeof(UserPreferenceKey), d.Name)
     *
     * @param name 偏好设置名称
     * @return UserPreferenceKey 枚举值
     */
    private UserPreferenceKey parseUserPreferenceKey(String name) {
        try {
            return UserPreferenceKey.valueOf(name);
        } catch (IllegalArgumentException ex) {
            log.warn("Unknown user preference key: {}", name);
            // 如果枚举值不存在，可以返回一个默认值或抛出异常
            // 这里选择抛出异常以保持与C#代码的一致性
            throw new BizException("Unknown user preference key: " + name);
        }
    }
}
