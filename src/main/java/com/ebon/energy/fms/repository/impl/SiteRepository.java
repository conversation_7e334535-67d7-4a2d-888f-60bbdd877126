package com.ebon.energy.fms.repository.impl;

import com.ebon.energy.fms.common.utils.TimeZoneConverterUtil;
import com.ebon.energy.fms.domain.vo.product.ProductInfoLightVO;
import com.ebon.energy.fms.mapper.third.SiteMapper;
import com.ebon.energy.fms.repository.ISiteRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.*;
import java.util.List;

@Service
@RequiredArgsConstructor
public class SiteRepository implements ISiteRepository {

    private final SiteMapper siteMapper;

    public LocalDate getBclTimeZoneIdForSite(String publicSiteId) {
        var bclTimeZoneId = siteMapper.getBclTimeZoneIdForSite(publicSiteId);

        if (bclTimeZoneId == null || bclTimeZoneId.isEmpty()) {
            throw new IllegalArgumentException("No time zone configured for given site");
        }

        // NOTE: 使用TimeZoneConverterUtil将BCL时区转换为ZoneId
        // 这类似于C#中的TimeZoneConverter.TZConvert.WindowsToIana
        var timeZone = TimeZoneConverterUtil.convertBclOrIanaToDateTimeZone(bclTimeZoneId);

        if (timeZone == null) {
            throw new IllegalArgumentException("No valid time zone configured for given site");
        }

        // 获取当前时间并转换为指定时区的本地日期
        // 这类似于C#中的ZonedClock和GetCurrentDate
        Instant currentInstant = Instant.now();
        ZonedDateTime zonedDateTime = currentInstant.atZone(timeZone);

        return zonedDateTime.toLocalDate();
    }

    public List<ProductInfoLightVO> getSiteProducts(String publicSiteId){
        return siteMapper.selectSiteProductsLight(publicSiteId);
    }

    /**
     * 获取站点的 LFDI (Long Form Device Identifier)
     * 基于 C# 代码逻辑实现的 Java 版本
     *
     * @param internalSiteId 内部站点ID，可为空
     * @return 站点的 LFDI，如果未找到则返回 Optional.empty()
     */
    public String getSiteLfdi(String siteId) {
        // 检查参数是否为空或者是空的 UUID
        if (!StringUtils.hasLength(siteId)) {
            return null;
        }

        return siteMapper.getSiteLfdi(siteId);
    }

}
