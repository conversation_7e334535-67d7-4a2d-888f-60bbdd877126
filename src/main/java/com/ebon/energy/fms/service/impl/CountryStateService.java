package com.ebon.energy.fms.service.impl;

import com.ebon.energy.fms.domain.entity.CountryStateDO;
import com.ebon.energy.fms.domain.vo.detail.CountryStateDTO;
import com.ebon.energy.fms.mapper.third.CountryStateMapper;
import com.ebon.energy.fms.service.ICountryStateFacadeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 国家州服务实现类
 * 实现国家和州相关的业务逻辑
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CountryStateService implements ICountryStateFacadeService {

    private final CountryStateMapper countryStateMapper;

    /**
     * 获取所有州信息
     * @param countryCode 国家代码，如果为null则获取所有国家的州信息
     * @return 州信息列表
     */
    @Override
    public List<CountryStateDTO> getAllStates(String countryCode) {
        try {
            log.debug("获取州信息，国家代码: {}", countryCode);
            
            // 从数据库查询州信息
            List<CountryStateDO> countryStateDOList = countryStateMapper.selectAllStates(countryCode);
            
            // 转换为DTO
            List<CountryStateDTO> result = countryStateDOList.stream()
                    .map(entity -> new CountryStateDTO(
                            entity.getId(),
                            entity.getCountry(),
                            entity.getState()
                    ))
                    .collect(Collectors.toList());
            
            log.debug("成功获取{}条州信息", result.size());
            return result;
            
        } catch (Exception e) {
            log.error("获取州信息失败，国家代码: {}", countryCode, e);
            throw new RuntimeException("获取州信息失败", e);
        }
    }
}
