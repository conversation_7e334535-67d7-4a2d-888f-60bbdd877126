package com.ebon.energy.fms.service;

import com.ebon.energy.fms.common.enums.AustralianState;
import com.ebon.energy.fms.domain.vo.detail.NMIValidationResult;

import java.time.LocalDateTime;

/**
 * NMI服务接口
 * 定义NMI（National Metering Identifier）相关的验证和操作方法
 */
public interface INMIService {

    /**
     * 根据州验证NMI
     * @param nmi NMI标识符
     * @param state 澳大利亚州
     * @param installationStartDate 安装开始日期
     * @return NMI验证结果
     */
    NMIValidationResult validationForState(String nmi, AustralianState state, LocalDateTime installationStartDate);

}
