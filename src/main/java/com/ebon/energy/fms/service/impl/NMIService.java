// Copyright (c) Redback Technologies. All Rights Reserved.

package com.ebon.energy.fms.service.impl;

import com.ebon.energy.fms.common.enums.AustralianState;
import com.ebon.energy.fms.config.PortalConfig;
import com.ebon.energy.fms.domain.vo.detail.NMISettingDto;
import com.ebon.energy.fms.domain.vo.detail.NMIValidationResult;
import com.ebon.energy.fms.domain.vo.detail.NMIValidationStateDto;
import com.ebon.energy.fms.service.INMIService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * NMI服务实现类
 * 提供NMI（National Metering Identifier）验证功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NMIService implements INMIService {

    private final PortalConfig portalConfig;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private NMISettingDto nmiSetting;

    /**
     * 初始化方法，在依赖注入完成后执行
     */
    @PostConstruct
    public void init() {
        this.nmiSetting = getNMISettingDto(portalConfig.getNmiSettings());
    }

    /**
     * 验证NMI长度和校验和
     * @param nmi NMI标识符
     * @return 如果NMI有效返回true，否则返回false
     */
    public boolean isValid(String nmi) {
        if (nmiSetting.isNmiValidationTurnedOff()) {
            return true;
        }

        return StringUtils.hasText(nmi) && 
               nmi.length() == 11 && 
               String.valueOf(getChecksum(nmi)).charAt(0) == nmi.charAt(10);
    }

    /**
     * 根据州验证NMI
     * @param nmi NMI标识符
     * @param state 澳大利亚州
     * @param installedDate 安装日期
     * @return NMI验证结果
     */
    @Override
    public NMIValidationResult validationForState(String nmi, AustralianState state, LocalDateTime installedDate) {
        NMIValidationResult nmiValidationResult = new NMIValidationResult(false, false, null);

        if (nmiSetting == null) {
            return nmiValidationResult;
        }

        if (nmiSetting.isNmiValidationTurnedOff()) {
            nmiValidationResult.setValid(true);
            return nmiValidationResult;
        }

        if (state == AustralianState.Unknown) { // 非澳大利亚州（例如新西兰）
            nmiValidationResult.setValid(true);
            return nmiValidationResult;
        }

        Optional<NMIValidationStateDto> validationState = Optional.ofNullable(nmiSetting.getNmiValidationStates())
                .flatMap(states -> states.stream()
                        .filter(e -> e.getState() == state)
                        .findFirst());

        if (!validationState.isPresent()) {
            nmiValidationResult.setValid(true);
            return nmiValidationResult;
        }

        LocalDateTime effectiveInstalledDate = installedDate != null ? installedDate : LocalDateTime.now();

        NMIValidationStateDto stateDto = validationState.get();
        if (stateDto.getMandatoryDate() != null && 
            !effectiveInstalledDate.isBefore(stateDto.getMandatoryDate())) {
            nmiValidationResult.setMandatory(true);
            nmiValidationResult.setLastDateNMIMandatoryRequired(
                effectiveInstalledDate.plusDays(stateDto.getDaysRequiredToInputNMIMandatory()));
        }

        nmiValidationResult.setValid(!stateDto.isValidation() || isValid(nmi));
        return nmiValidationResult;
    }

    /**
     * 计算LUHN-10校验和
     * 参考：https://www.aemo.com.au/-/media/Files/PDF/0610-0008-pdf.pdf 附录1
     * @param nmi NMI标识符
     * @return 校验和
     */
    private static int getChecksum(String nmi) {
        int sum = 0;
        for (int i = 0; i < Math.min(10, nmi.length()); i++) {
            int digit = Character.getNumericValue(nmi.charAt(i));
            if (i % 2 == 1) { // 每隔一位数字乘以2
                digit *= 2;
            }
            sum += sumDigits(digit); // 对所有数字求和
        }
        return (10 - (sum % 10)) % 10; // 到下一个10的倍数的位数
    }

    /**
     * 计算数字各位数之和
     * @param n 输入数字
     * @return 各位数之和
     */
    private static int sumDigits(int n) {
        int sum = 0;
        while (n != 0) {
            sum += n % 10;
            n /= 10;
        }
        return sum;
    }

    /**
     * 解析NMI设置JSON字符串
     * @param nmiSetting NMI设置JSON字符串
     * @return NMI设置DTO对象
     */
    private NMISettingDto getNMISettingDto(String nmiSetting) {
        NMISettingDto nmiSettingDto = new NMISettingDto();
        nmiSettingDto.setNmiValidationTurnedOff(true);

        if (StringUtils.hasText(nmiSetting)) {
            try {
                nmiSettingDto = objectMapper.readValue(nmiSetting, NMISettingDto.class);
                log.debug("NMI设置: {}", nmiSetting);
            } catch (Exception ex) {
                String msg = String.format(
                    "NMI设置格式不正确: %s. \r\n正确格式示例: " +
                    "{'ValidationStates': [" +
                    "{'State':'SA','MandatoryDate':'26/09/2020','DaysRequiredToInputNMIMandatory':30,'Validation':'true'}," +
                    "{'State':'WA','MandatoryDate':'14/02/2022','DaysRequiredToInputNMIMandatory':30,'Validation':'true'}," +
                    "{'State':'QLD','MandatoryDate':'','Validation':'true'}," +
                    "{'State':'ACT','MandatoryDate':'','Validation':'true'}" +
                    "], 'NMIValidationTurnedOff': false}.",
                    nmiSetting);
                log.warn(msg + ex.getMessage());
            }
        } else {
            log.warn("NMI设置缺失。");
        }

        return nmiSettingDto;
    }
}
