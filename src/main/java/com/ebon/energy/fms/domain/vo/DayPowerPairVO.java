package com.ebon.energy.fms.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

@Data
public class DayPowerPairVO {
    
    private String label;
    private BigDecimal watts;
    private BigDecimal kwh;
    private Boolean accurate;

    public DayPowerPairVO(String label, BigDecimal watts, boolean accurate) {
        this.label = Objects.requireNonNull(label, "not must  be null");
        this.watts = watts;
        this.kwh = getKwh(watts);
        this.accurate = accurate;
    }

    private BigDecimal getKwh(BigDecimal wh) {
        if (wh == null) {
            return null;
        }

        return wh.divide(WattHour.Kilo.getValue(), 1, BigDecimal.ROUND_HALF_UP);
    }

}
