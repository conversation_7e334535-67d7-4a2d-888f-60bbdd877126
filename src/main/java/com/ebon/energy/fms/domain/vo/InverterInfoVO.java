package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.domain.vo.telemetry.SystemStatus;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@Data
public class InverterInfoVO {

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 逆变器名称
     */
    private String inverterName;
    
    private String plantName;

    /**
     * 额定功率
     */
    private String ratedPower;

    /**
     * 是否在线
     */
    private Boolean isOnline;

    /**
     * 是否小时在线
     */
    private Boolean isHourlyOnline;

    /**
     * 是否日在线
     */
    private Boolean isDailyOnline;

    /**
     * 逆变器模式
     */
    private String inverterMode;

    /**
     * ross版本号
     */
    private String rossVersion;

    /**
     * 是否ross v1版本
     */
    private Boolean isRoss1;

    /**
     * 固件版本
     */
    private String firmwareVersion;

    /**
     * 硬件配置
     */
    private String hardwareConfig;

    /**
     * 逆变器型号DisplayName
     */
    private String modelName;

    /**
     * 逆变器型号
     */
    private String inverterModelName;

    /**
     * 安装时间
     */
    private String installationDate;

    /**
     * 是否在保修期
     */
    private Boolean isInWarranty;

    /**
     * 保修结束时间
     */
    private String warrantyEndDate;

    /**
     * 最后连接时间
     */
    private String lastConnected;

    /**
     * 0-未处理 1-已处理
     */
    private Integer errorStatus;

    /**
     * 站点ID
     */
    private String siteId;

    /**
     * 地址
     */
    private String address;

    /**
     * owner联系方式
     */
    private String ownerContact;

    /**
     * owner邮箱
     */
    private String ownerEmail;
    
    private String ownerName;
    
    private String ownerFirstName;
    
    private String ownerLastName;

    /**
     * 站点nmi
     */
    private String nmi;

    /**
     * settings
     */
    private InverterSettingsVO settings;

    /**
     * 今日pv统计
     */
    private String todayPvTotal;

    /**
     * 月pv统计
     */
    private String monthPvTotal;

    /**
     * 年pv统计
     */
    private String yearPvTotal;


    /**
     * 总pv统计
     */
    private String allTimePvTotal;

    /**
     * 总输入统计
     */
    private String allTimeTotalImport;

    /**
     * 总输出统计
     */
    private String allTimeTotalExport;

    /**
     * 最后遥测接收时间
     */
    private Timestamp lastSystemStatusReceived;

    /**
     * 最池序列号列表
     */
    private List<String> batterySerialNumbers;

    /**
     * 电池型号
     */
    private String batteryModel;

    /**
     * desired电池数量
     */
    private Integer desiredBatteries;

    /**
     * reported电池数量
     */
    private Integer reportedBatteries;

    /**
     * 系统状态
     */
    private SystemStatus systemStatus;
    
    private String maintainingInstaller;
    
    private String maintainingInstallerEmail;
    
    private String maintainingInstallerPhone;
    
    private SupportNotesVO supportNotes;

}