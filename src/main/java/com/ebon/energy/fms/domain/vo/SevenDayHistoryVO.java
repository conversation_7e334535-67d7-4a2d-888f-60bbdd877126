package com.ebon.energy.fms.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Data
public class SevenDayHistoryVO {
    private String id;
    private BigDecimal pastSevenDaysWatts;
    private BigDecimal pastSevenDaysKwh;
    private Integer percentageComparisonPastSevenDays;
    private List<DayPowerPairVO> data;
    private boolean applyChartSafeDataFeature;

    public SevenDayHistoryVO(
            String id,
            BigDecimal pastSevenDaysWatts,
            Integer percentageComparisonPastSevenDays,
            List<DayPowerPairVO> data,
            boolean applyChartSafeDataFeature) {
        this.id = Objects.requireNonNull(id, "id must not be null");
        this.pastSevenDaysWatts = pastSevenDaysWatts;
        this.pastSevenDaysKwh = getKwh(pastSevenDaysWatts);
        this.percentageComparisonPastSevenDays = percentageComparisonPastSevenDays;
        this.data = Objects.requireNonNull(data, "data must not be null");
        this.applyChartSafeDataFeature = applyChartSafeDataFeature;
    }

    private BigDecimal getKwh(BigDecimal wh) {
        if (wh == null) {
            return null;
        }

        return wh.divide(WattHour.Kilo.getValue(), 1, BigDecimal.ROUND_HALF_UP);
    }

}