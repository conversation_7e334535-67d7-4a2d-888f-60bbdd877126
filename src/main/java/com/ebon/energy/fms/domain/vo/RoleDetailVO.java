package com.ebon.energy.fms.domain.vo;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@Data
public class RoleDetailVO {

    /**
     * 角色ID
     */
    private Integer id;

    /**
     * 角色名
     */
    private String roleName;

    /**
     * 状态：true 启用；false 禁用
     */
    private Boolean status;

    /**
     * 数据权限类型 0-All 1-Owner 2-Group 3-Self
     */
    private Integer dataPermission;

    /**
     * 是否超级管理角色，超级管理角色不能修改
     */
    private Boolean isSuperRole;

    /**
     * 权限code列表
     */
    private List<String> permissionCodes;

}
