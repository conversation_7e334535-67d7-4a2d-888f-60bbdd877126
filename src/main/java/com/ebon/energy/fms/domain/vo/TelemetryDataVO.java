package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.domain.vo.telemetry.RossTelemetry;
import com.ebon.energy.fms.domain.vo.telemetry.SystemStatus;
import lombok.Data;

@Data
public class TelemetryDataVO {

    /**
     * 逆变器序列号
     */
    private String serialNumber;

    /**
     * 遥测数据
     */
    private String telemetry;

    /**
     * 系统状态
     */
    private String systemStatus;
    
    private RossTelemetry telemetryVO;
    
    private SystemStatus systemStatusVO;

}