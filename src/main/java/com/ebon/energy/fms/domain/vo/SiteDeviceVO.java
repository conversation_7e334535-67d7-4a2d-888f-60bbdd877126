package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.common.enums.BalancingMethods;
import com.ebon.energy.fms.common.enums.Phase;
import com.ebon.energy.fms.common.enums.PhaseRole;
import lombok.Data;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.ZonedDateTime;

@Data
public class SiteDeviceVO {

    private String serialNumber;
    private String latestSystemStatus;
    private ZonedDateTime lastSystemStatusReceivedUtc;
    private boolean isInWarranty;
    private String bclTimeZoneId;
    private String maintainingInstallerId;
    private ZonedDateTime supportsLoadContributors;
    private boolean hasReports;
    private BalancingMethods energyBalancingMethod;
    private Phase phase;
    private PhaseRole phaseRole;
    private ZonedDateTime installationDateUtc;

    public LocalDate getSupportsLoadContributors() {
        return supportsLoadContributors != null
                ? supportsLoadContributors.toLocalDate()
                : null;
    }
}