package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ebon.energy.fms.common.enums.PhotoFeaturingEnum;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 安装照片实体类
 * 对应数据库表：InstallationPhotos（假设的表名）
 */
@Data
@TableName("InstallationPhotos")
public class InstallationPhotoDO {

    /**
     * 照片ID
     */
    @TableId(value = "Id")
    private String id;

    /**
     * 安装ID
     */
    @TableField(value = "InstallationId")
    private Integer installationId;

    /**
     * 照片特征类型
     */
    @TableField(value = "Featuring")
    private Integer featuring;

    /**
     * 添加日期（UTC）
     */
    @TableField(value = "DateAddedUtc")
    private Timestamp dateAddedUtc;

    /**
     * 照片URI
     */
    @TableField(value = "Uri")
    private String uri;

    /**
     * 缩略图URI
     */
    @TableField(value = "ThumbnailUri")
    private String thumbnailUri;

    /**
     * 删除请求日期（UTC）- 用于软删除
     */
    @TableField(value = "DeletionRequestedOnUtc")
    private Timestamp deletionRequestedOnUtc;

    /**
     * 用户ID
     */
    @TableField(value = "UserId")
    private String userId;
}
