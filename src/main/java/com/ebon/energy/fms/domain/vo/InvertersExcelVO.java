package com.ebon.energy.fms.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvertersExcelVO {

    @ExcelProperty("SerialNumber")
    @ColumnWidth(25)
    private String serialNumber;

    @ExcelProperty("RossVersion")
    @ColumnWidth(30)
    private String rossVersion;

    @ExcelProperty("FirmwareVersion")
    @ColumnWidth(15)
    private String firmwareVersion;

    @ExcelProperty("InverterModelName")
    @ColumnWidth(20)
    private String inverterModelName;



    public static InvertersExcelVO convert(InvertersVO invertersVO) {
        return InvertersExcelVO.builder()
                .serialNumber(invertersVO.getSerialNumber())
                .rossVersion(invertersVO.getRossVersion())
                .firmwareVersion(invertersVO.getFirmwareVersion())
                .inverterModelName(invertersVO.getInverterModelName())
                .build();
    }
}
