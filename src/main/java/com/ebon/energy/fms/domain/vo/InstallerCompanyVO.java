package com.ebon.energy.fms.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
public class InstallerCompanyVO {

    /**
     * 安装商ID
     */
    private String id;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    private String addressLineOne;

    private String AddressLineTwo;

    /**
     * 区
     */
    private String suburb;

    /**
     * 州
     */
    private String state;

    /**
     * 国家
     */
    private String country;
    
    /**
     * 邮编
     */
    private String postCode;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 安装数量
     */
    private Integer installedCount;

}