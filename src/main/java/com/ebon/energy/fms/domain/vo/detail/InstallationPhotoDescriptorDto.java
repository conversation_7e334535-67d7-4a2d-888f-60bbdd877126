package com.ebon.energy.fms.domain.vo.detail;

import com.ebon.energy.fms.common.enums.PhotoFeaturingEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.net.URI;
import java.time.Instant;
import java.util.UUID;

@Data
public class InstallationPhotoDescriptorDto {

    @JsonProperty("Id")
    private final String id;

    @JsonProperty("Featuring")
    private final PhotoFeaturingEnum featuring;

    @JsonProperty("DateCreated")
    private final Instant dateCreated;

    @JsonProperty("Uri")
    private final String uri;

    @JsonProperty("ThumbnailUri")
    private final String thumbnailUri;
}