package com.ebon.energy.fms.domain.vo.detail;

import com.ebon.energy.fms.common.enums.PhotoFeaturingEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.net.URI;
import java.time.Instant;
import java.util.UUID;

@Data
public class InstallationPhotoDescriptorDto {

    @JsonProperty("Id")
    private String id;

    @JsonProperty("Featuring")
    private PhotoFeaturingEnum featuring;

    @JsonProperty("DateCreated")
    private Instant dateCreated;

    @JsonProperty("Uri")
    private String uri;

    @JsonProperty("ThumbnailUri")
    private String thumbnailUri;

    public InstallationPhotoDescriptorDto() {
    }

    public InstallationPhotoDescriptorDto(String id, PhotoFeaturingEnum featuring, Instant dateCreated, String uri, String thumbnailUri) {
        this.id = id;
        this.featuring = featuring;
        this.dateCreated = dateCreated;
        this.uri = uri;
        this.thumbnailUri = thumbnailUri;
    }
}