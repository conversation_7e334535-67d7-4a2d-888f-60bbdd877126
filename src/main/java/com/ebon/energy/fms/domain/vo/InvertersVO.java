package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.domain.vo.telemetry.Error;
import lombok.Data;

import java.util.List;

@Data
public class InvertersVO {

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 是否在线
     */
    private Boolean isOnline;

    /**
     * 是否小时在线
     */
    private Boolean isHourlyOnline;

    /**
     * 是否日在线
     */
    private Boolean isDailyOnline;

    /**
     * 是否Watchdog在线
     */
    private Boolean isWatchdogOnline;

    /**
     * 是否SCCM在线
     */
    private Boolean isSCCMOnline;

    /**
     * 电池厂商
     */
    private String detectedBatteryManufacturer;

    /**
     * 电池型号
     */
    private String detectedBatteryModel;

    /**
     * ross版本号
     */
    private String rossVersion;

    /**
     * windows版本号
     */
    private String windowsVersion;

    /**
     * windows版本号 - SS
     */
    private String windowsVersionSS;

    /**
     * 固件版本号
     */
    private String firmwareVersion;

    /**
     * watchDog版本号
     */
    private String watchDogVersion;

    private String edgeId;

    /**
     * watchDog设备ID
     */
    private String watchdogDeviceId;

    /**
     * 逆变器型号
     */
    private String inverterModelName;

    /**
     * 逆变器模式
     */
    private String inverterMode;

    /**
     * 安装公司
     */
    private String installerCompany;

    /**
     * 是否需要关注
     */
    private Boolean needAttention;

    /**
     * 电池状态
     */
    private String batteryStatus;

    /**
     * 0-未处理 1-已处理
     */
    private Integer errorStatus;

    /**
     * 错误列表
     */
    private List<Error> errors;

}