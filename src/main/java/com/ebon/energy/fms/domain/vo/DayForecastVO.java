package com.ebon.energy.fms.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

import static com.ebon.energy.fms.util.TimeUtils.getDayOfTheWeekOrToday;
import static com.ebon.energy.fms.util.TimeUtils.getLocalDateStr;

@Data
public class DayForecastVO {

    /**
     * 日期
     */
    private String date;

    /**
     * 星期
     */
    private String label;

    /**
     * 电量（瓦时）
     */
    private BigDecimal wh;

    /**
     * 电量（千瓦时）
     */
    private BigDecimal kwh;

    /**
     * 预测电量（瓦时）
     */
    private BigDecimal forecastWh;

    /**
     * 预测电量（千瓦时）
     */
    private BigDecimal forecastKwh;

    public DayForecastVO(String date, String label, BigDecimal wh, BigDecimal forecastKwh) {
        this.date = date;
        this.label = label;
        this.wh = wh;
        this.kwh = WattHour.getKwh(wh);
        this.forecastWh = forecastKwh != null ? forecastKwh.multiply(new BigDecimal(1000)).setScale(1, BigDecimal.ROUND_HALF_UP) : null;
        this.forecastKwh = forecastKwh != null ? forecastKwh.setScale(1, BigDecimal.ROUND_HALF_UP) : null;
    }

    public static DayForecastVO empty(LocalDate date) {
        return new DayForecastVO(
                getLocalDateStr(date),
                getDayOfTheWeekOrToday(date, LocalDate.now()),
                null,
                null
        );
    }

}
