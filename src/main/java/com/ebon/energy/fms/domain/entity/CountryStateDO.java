package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 国家州实体类
 * 对应数据库表CountryStates
 */
@Data
@TableName("CountryStates")
public class CountryStateDO {

    /**
     * 主键ID
     */
    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    /**
     * 国家名称
     */
    @TableField(value = "Country")
    private String country;

    /**
     * 州/省名称
     */
    @TableField(value = "State")
    private String state;
}
