package com.ebon.energy.fms.domain.vo;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

@JsonAutoDetect(
        fieldVisibility = JsonAutoDetect.Visibility.ANY,      // 直接使用字段名
        getterVisibility = JsonAutoDetect.Visibility.NONE,    // 忽略getter
        setterVisibility = JsonAutoDetect.Visibility.NONE     // 忽略setter
)
public class DataWithPermalinkVO<T> {
    private T Data;
    //private MetadataVO Meta;

    public DataWithPermalinkVO(T data, String permalink) {
        this.Data = data;
        /*this.Meta = new MetadataVO();
        this.Meta.setPermalink(permalink);*/
    }

    public T getData() {
        return Data;
    }

    public void setData(T data) {
        this.Data = data;
    }

    /*public MetadataVO getMeta() {
        return Meta;
    }

    public void setMeta(MetadataVO meta) {
        this.Meta = meta;
    }*/

}