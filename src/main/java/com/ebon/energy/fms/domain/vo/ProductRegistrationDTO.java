package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.domain.entity.AddressesDO;
import com.ebon.energy.fms.domain.vo.detail.SolarPanel;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 产品注册数据传输对象
 * 用于产品注册和安装信息的传输
 */
@Data
public class ProductRegistrationDTO {

    @JsonProperty("SerialNumber")
    private String serialNumber;

    @JsonProperty("SolarPanelManufacturer")
    private String solarPanelManufacturer;

    @JsonProperty("SolarPanelType")
    private String solarPanelType;

    @JsonProperty("IsOffgrid")
    private boolean isOffgrid;

    @JsonProperty("ConnectionPointIdentifier")
    private String connectionPointIdentifier;

    @JsonProperty("ConnectionPointIdentifierOptOut")
    private boolean connectionPointIdentifierOptOut;

    @JsonProperty("SolarPanels")
    private List<SolarPanel> solarPanels;

    @JsonProperty("InstallationAddress")
    private AddressesDO installationAddress;
}
