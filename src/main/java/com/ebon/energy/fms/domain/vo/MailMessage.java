package com.ebon.energy.fms.domain.vo;

import lombok.Data;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Data
public class MailMessage {
    //发送方
    private String from;
    //接收方邮件
    private List<String> to = new ArrayList<>();
    //抄送方
    private List<String> cc = new ArrayList<>();
    private List<String> bcc = new ArrayList<>();
    //标题
    private String subject;
    private String body;

    //HTML内容
    private String content;

    public static void checkMailMessage(MailMessage mailMessage) {
        if(Strings.isEmpty(mailMessage.getFrom())) {
            throw new IllegalArgumentException("From email can not be null");
        }

        if(Strings.isEmpty(mailMessage.getSubject())) {
            throw new IllegalArgumentException("Email subject can not be null");
        }

        if(Strings.isEmpty(mailMessage.getContent())) {
            throw new IllegalArgumentException("Email content can not be null");
        }

        if(CollectionUtils.isEmpty(mailMessage.getTo())) {
            throw new IllegalArgumentException("To email can not be null");
        }
    }

}
